
def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new RuntimeException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"
apply plugin: 'com.google.gms.google-services'

android {
    namespace 'br.com.unimedfortaleza.minhaunimed'
    compileSdkVersion 35

    ndkVersion "26.1.10909125"

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }
    compileOptions {
            sourceCompatibility JavaVersion.VERSION_1_8
            targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    lintOptions {
        disable 'InvalidPackage'
        checkReleaseBuilds false
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "br.com.unimedfortaleza.minhaunimed"
        minSdkVersion 24
        targetSdkVersion 35
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }

    flavorDimensions "default"
    
    signingConfigs {
       fortaleza {
            def keystoreProperties = new Properties()
            def keystorePropertiesFile = rootProject.file('key.properties')
            if (keystorePropertiesFile.exists()) {
                keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
            }
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
       }
       sobral {
            def keystoreProperties = new Properties()
            def keystorePropertiesFile = rootProject.file('key_sobral.properties')
            if (keystorePropertiesFile.exists()) {
                keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
            }
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
       }
       cariri {
            def keystoreProperties = new Properties()
            def keystorePropertiesFile = rootProject.file('key_cariri.properties')
            if (keystorePropertiesFile.exists()) {
                keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
            }
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
       }
        ceara {
            def keystoreProperties = new Properties()
            def keystorePropertiesFile = rootProject.file('key_ceara.properties')
            if (keystorePropertiesFile.exists()) {
                keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
            }
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
       }
    }

    productFlavors { 
        fortaleza {
            dimension "default"
            manifestPlaceholders = [appName: "Minha Unimed"]
            signingConfig signingConfigs.fortaleza
        }
        sobral {
            dimension "default"
            applicationId "br.com.unimedfortaleza.sobral.cooperado"
            manifestPlaceholders = [appName: "Minha Unimed Sobral"]
            signingConfig signingConfigs.sobral
        }
        cariri {
            dimension "default"
            applicationId "br.com.unimedfortaleza.cariri.cooperado"
            manifestPlaceholders = [appName: "Minha Unimed Cariri"]
            signingConfig signingConfigs.cariri
        }
        ceara {
            dimension "default"
            applicationId "br.com.unimedfortaleza.ceara.cooperado"
            manifestPlaceholders = [appName: "Minha Unimed Ceara"]
            signingConfig signingConfigs.ceara
        }                
    }
   
}

flutter {
    source '../..'
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
}


